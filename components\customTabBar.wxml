<!--components/customTabBar.wxml-->
<view class="tab-bar">
  <view
    class="tab-bar-item {{selected === index ? 'tab-bar-item-active' : ''}}"
    wx:for="{{list}}"
    wx:key="index"
    data-path="{{item.pagePath}}"
    data-index="{{index}}"
    bindtap="switchTab"
  >
    <image
      src="{{selected === index ? item.selectedIconPath : item.iconPath}}"
      class="tab-bar-icon"
    ></image>
    <text class="tab-bar-text" style="color: {{selected === index ? selectedColor : color}}">
      {{item.text}}
    </text>
  </view>
</view>