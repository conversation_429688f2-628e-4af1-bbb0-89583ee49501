/* pages/exam/index.wxss */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.identity-tag {
  background: #667eea;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.exam-list {
  margin-bottom: 120rpx;
}

.exam-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.exam-info {
  flex: 1;
}

.exam-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.exam-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.exam-date, .exam-score {
  font-size: 26rpx;
  color: #666;
}

.exam-score {
  color: #52c41a;
  font-weight: bold;
}

.exam-status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.exam-status.active {
  background: #fff7e6;
  color: #fa8c16;
}

.exam-status.finished {
  background: #e8f5e8;
  color: #52c41a;
}

.exam-status.pending {
  background: #f0f0f0;
  color: #999;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
}

.empty text {
  font-size: 28rpx;
}
