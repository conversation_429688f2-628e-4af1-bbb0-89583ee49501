// pages/course/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    identity: '',
    courses: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkIdentity();
    this.loadCourses();
  },

  /**
   * 检查用户身份
   */
  checkIdentity() {
    const identity = app.getUserIdentity();
    if (!identity) {
      app.navigateToIdentitySelect();
      return;
    }
    this.setData({
      identity: identity
    });
  },

  /**
   * 加载课程数据
   */
  loadCourses() {
    // 这里可以根据身份加载不同的课程数据
    const mockCourses = [
      {
        id: 1,
        name: '数学基础课程',
        teacher: '张老师',
        students: 25,
        status: '进行中'
      },
      {
        id: 2,
        name: '英语口语训练',
        teacher: '李老师', 
        students: 18,
        status: '已结束'
      }
    ];
    
    this.setData({
      courses: mockCourses
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkIdentity();
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadCourses();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
