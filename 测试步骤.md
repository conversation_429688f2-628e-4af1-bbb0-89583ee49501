# TabBar 显示问题测试步骤

## 🔍 立即测试步骤

### 1. 清除缓存并重新编译
1. 在微信开发者工具中点击"清缓存" -> "全部清除"
2. 重新编译项目
3. 观察控制台输出

### 2. 查看控制台日志
启动小程序后，控制台应该显示：
```
自定义tabBar组件已加载
开始更新tabBar配置
当前身份: null (首次启动)
身份为空，隐藏tabBar
```

### 3. 完成身份选择流程
1. 应该自动跳转到身份选择页面
2. 选择任意身份（如"学生"）
3. 点击确认按钮
4. 应该跳转到课程页面

### 4. 检查tabBar是否显示
在课程页面，控制台应该显示：
```
tabBar页面显示
开始更新tabBar配置
当前身份: student
tabBar配置: [课程、考情、我的的配置数组]
```

## 🐛 如果tabBar仍然不显示

### 检查点1：确认页面路径
确保当前页面路径在 `app.json` 的 `tabBar.list` 中：
- `pages/course/index` ✅
- `pages/exam/index` ✅  
- `pages/student/index` ✅
- `pages/teacher/index` ✅
- `pages/user/center` ✅

### 检查点2：确认自定义tabBar文件结构
```
项目根目录/
├── custom-tab-bar/
│   ├── index.js    ✅
│   ├── index.wxml  ✅
│   ├── index.wxss  ✅
│   └── index.json  ✅
```

### 检查点3：查看具体错误
如果控制台有错误信息，请重点关注：
1. 图片加载失败
2. JavaScript运行错误
3. 组件加载失败

## 🔧 临时解决方案

如果图标导致问题，可以临时使用文字图标：

### 修改 custom-tab-bar/index.wxml
```xml
<view class="tab-bar">
  <view 
    class="tab-bar-item {{selected === index ? 'tab-bar-item-active' : ''}}"
    wx:for="{{list}}" 
    wx:key="index"
    data-path="{{item.pagePath}}"
    data-index="{{index}}"
    bindtap="switchTab"
  >
    <!-- 临时使用文字替代图标 -->
    <text class="tab-bar-emoji">{{item.emoji || '📱'}}</text>
    <text class="tab-bar-text" style="color: {{selected === index ? selectedColor : color}}">
      {{item.text}}
    </text>
  </view>
</view>
```

### 修改 custom-tab-bar/index.wxss
```css
.tab-bar-emoji {
  font-size: 40rpx;
  margin-bottom: 6rpx;
}
```

### 修改 app.js 中的配置
在 `getTabBarConfig` 方法中添加emoji字段：
```javascript
{
  text: "课程",
  pagePath: "pages/course/index",
  emoji: "📚",
  iconPath: "/images/home.png",
  selectedIconPath: "/images/home-active.png"
}
```

## 📱 预期效果

### 学生身份
底部应显示3个菜单：📚课程 | 📝考情 | 👤我的

### 授课老师身份  
底部应显示4个菜单：📚课程 | 📝考情 | 👥学员 | 👤我的

### 管理老师身份
底部应显示4个菜单：📚课程 | 👥学员 | 👨‍🏫授课老师 | 👤我的

## 🚨 紧急调试

如果以上都不行，请在 `custom-tab-bar/index.wxml` 最前面添加调试信息：

```xml
<!-- 调试信息 -->
<view style="position: fixed; top: 0; left: 0; background: red; color: white; padding: 10rpx; z-index: 9999;">
  TabBar组件已加载，菜单数量: {{list.length}}
</view>

<!-- 原有的tabBar代码 -->
<view class="tab-bar">
  ...
</view>
```

这样可以确认组件是否正常加载和数据是否正确。

## 📞 需要提供的信息

如果问题仍然存在，请提供：
1. 控制台的完整日志截图
2. 当前显示的页面截图  
3. 身份选择是否成功
4. 是否能正常在页面间跳转
