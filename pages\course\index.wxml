<!--pages/course/index.wxml-->
<view class="container">
  <view class="header">
    <view class="title">课程管理</view>
    <view class="identity-tag">{{identity === 'student' ? '学生' : identity === 'teacher' ? '授课老师' : '管理老师'}}</view>
  </view>

  <view class="course-list">
    <view class="course-item" wx:for="{{courses}}" wx:key="id">
      <view class="course-info">
        <view class="course-name">{{item.name}}</view>
        <view class="course-details">
          <text class="teacher">授课老师：{{item.teacher}}</text>
          <text class="students">学员数：{{item.students}}人</text>
        </view>
      </view>
      <view class="course-status {{item.status === '进行中' ? 'active' : 'finished'}}">
        {{item.status}}
      </view>
    </view>
  </view>

  <view wx:if="{{courses.length === 0}}" class="empty">
    <text class="empty-icon">📚</text>
    <text>暂无课程数据</text>
  </view>
</view>
