<!--pages/student/index.wxml-->
<view class="container">
  <view class="header">
    <view class="title">学员管理</view>
    <view class="identity-tag">{{identity === 'teacher' ? '授课老师' : '管理老师'}}</view>
  </view>

  <view class="student-list">
    <view class="student-item" wx:for="{{students}}" wx:key="id">
      <view class="student-avatar">
        <image src="{{item.avatar}}" mode="aspectFill"></image>
      </view>
      <view class="student-info">
        <view class="student-name">{{item.name}}</view>
        <view class="student-course">课程：{{item.course}}</view>
        <view class="student-login">最后登录：{{item.lastLogin}}</view>
      </view>
      <view class="student-progress">
        <view class="progress-text">{{item.progress}}%</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{item.progress}}%"></view>
        </view>
      </view>
    </view>
  </view>

  <view wx:if="{{students.length === 0}}" class="empty">
    <image src="/images/empty.png" mode="aspectFit"></image>
    <text>暂无学员数据</text>
  </view>
</view>
