/* pages/identity/select.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 80rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.identity-list {
  margin-bottom: 60rpx;
}

.identity-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 4rpx solid transparent;
}

.identity-item.selected {
  border-color: #667eea;
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.identity-icon {
  width: 100rpx;
  height: 100rpx;
  margin-right: 30rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-icon {
  font-size: 60rpx;
  line-height: 1;
}

.identity-info {
  flex: 1;
}

.identity-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.identity-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.identity-check {
  width: 40rpx;
  height: 40rpx;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #ddd;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.check-icon.checked {
  border-color: #667eea;
  background: #667eea;
}

.check-icon.checked::after {
  content: '';
  position: absolute;
  left: 8rpx;
  top: 4rpx;
  width: 12rpx;
  height: 20rpx;
  border: 4rpx solid #fff;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background: #fff;
  border-top: 1rpx solid #e5e5e5;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  background: #f7f7f7;
  color: #999;
  border-radius: 8rpx;
  font-size: 32rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.confirm-btn.active {
  background: #07c160;
  color: #fff;
}

.confirm-btn.active::after {
  border: none;
}

.confirm-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}
