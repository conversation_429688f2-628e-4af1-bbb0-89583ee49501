// pages/user/center.js
const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    identity: '',
    identityName: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserInfo();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const identity = app.getUserIdentity();
    const identityNames = {
      student: '学生',
      teacher: '授课老师',
      admin: '管理老师'
    };

    this.setData({
      identity: identity,
      identityName: identityNames[identity] || '未知'
    });
  },

  /**
   * 切换身份
   */
  switchIdentity() {
    wx.showModal({
      title: '切换身份',
      content: '确定要重新选择身份吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除身份信息
          wx.removeStorageSync('user_identity');
          wx.removeStorageSync('has_selected_identity');
          app.globalData.identity = null;
          app.globalData.hasSelectedIdentity = false;

          // 跳转到身份选择页面
          wx.navigateTo({
            url: '/pages/identity/select'
          });
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadUserInfo();
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      const identity = app.getUserIdentity();
      let selectedIndex = 2; // 默认我的页面索引
      if (identity === 'student') {
        selectedIndex = 2;
      } else if (identity === 'teacher') {
        selectedIndex = 3;
      } else if (identity === 'admin') {
        selectedIndex = 3;
      }
      this.getTabBar().setData({
        selected: selectedIndex
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})