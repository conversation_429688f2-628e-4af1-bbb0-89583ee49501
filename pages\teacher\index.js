// pages/teacher/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    identity: '',
    teachers: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkIdentity();
    this.loadTeachers();
  },

  /**
   * 检查用户身份
   */
  checkIdentity() {
    const identity = app.getUserIdentity();
    if (!identity || identity !== 'admin') {
      app.navigateToIdentitySelect();
      return;
    }
    this.setData({
      identity: identity
    });
  },

  /**
   * 加载授课老师数据
   */
  loadTeachers() {
    const mockTeachers = [
      {
        id: 1,
        name: '张老师',
        avatar: '/images/teacher1.png',
        subject: '数学',
        courses: 3,
        students: 45,
        experience: '5年教学经验'
      },
      {
        id: 2,
        name: '李老师',
        avatar: '/images/teacher2.png',
        subject: '英语',
        courses: 2,
        students: 32,
        experience: '8年教学经验'
      },
      {
        id: 3,
        name: '王老师',
        avatar: '/images/teacher3.png',
        subject: '物理',
        courses: 4,
        students: 58,
        experience: '10年教学经验'
      }
    ];
    
    this.setData({
      teachers: mockTeachers
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkIdentity();
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateTabBar();
      this.getTabBar().setData({
        selected: 2
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadTeachers();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
