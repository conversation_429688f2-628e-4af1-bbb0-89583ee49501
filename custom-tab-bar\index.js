// 自定义tabBar组件
const app = getApp();

Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#667eea",
    list: []
  },

  lifetimes: {
    attached() {
      console.log('自定义tabBar组件已加载');
      this.updateTabBar();
    }
  },

  pageLifetimes: {
    show() {
      console.log('tabBar页面显示');
      this.updateTabBar();
    }
  },

  methods: {
    /**
     * 更新tabBar配置
     */
    updateTabBar() {
      console.log('开始更新tabBar配置');
      const identity = app.getUserIdentity();
      console.log('当前身份:', identity);

      if (!identity) {
        console.log('身份为空，隐藏tabBar');
        this.setData({
          list: []
        });
        return;
      }

      const tabBarConfig = app.getTabBarConfig(identity);
      console.log('tabBar配置:', tabBarConfig);

      this.setData({
        list: tabBarConfig
      });

      // 根据当前页面设置选中状态
      this.updateSelected();
    },

    /**
     * 更新选中状态
     */
    updateSelected() {
      const pages = getCurrentPages();
      if (pages.length === 0) return;

      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;

      const list = this.data.list;
      for (let i = 0; i < list.length; i++) {
        if (list[i].pagePath === currentRoute) {
          this.setData({
            selected: i
          });
          break;
        }
      }
    },

    /**
     * tabBar点击事件
     */
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      
      wx.switchTab({
        url: '/' + url
      });
      
      this.setData({
        selected: data.index
      });
    }
  }
});
