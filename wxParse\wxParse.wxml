<template name="wxParse11">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse10">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse9">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse8">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse7">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse6">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse5">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse4">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse3">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse2">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse1">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse0">
  <block wx:if="{{item.node=='element'}}">
    <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
      <template is="wxParse1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </button>
    <view class="{{item.classStr}} wxParse-li" style="{{item.styleStr}}" wx:elif="{{item.tag=='li'}}">
      <view class="{{item.classStr}} wxParse-li-inner">
        <view class="{{item.classStr}} wxParse-li-text">
          <view class="{{item.classStr}} wxParse-li-circle"></view>
        </view>
        <view class="{{item.classStr}} wxParse-li-text">
          <template is="wxParse1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
        </view>
      </view>
    </view>
    <template is="wxParseVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
    <template is="wxParseImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
    <view bindtap="wxParseTagATap" class="wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-c="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
      <template is="wxParse1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
      <template is="wxParse1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
      <template is="wxParse1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
    <view class="{{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
      <template is="wxParse1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="this"></template>
    </view>
  </block>
  <template is="WxEmojiView" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParse">
  <template is="wxParse0" data="{{item:item}}" wx:for="{{wxParseData}}" wx:key="this"></template>
</template>
<template name="WxEmojiView">
  <view class="WxEmojiView wxParse-inline" style="{{item.styleStr}}">
    <block wx:for="{{item.textArray}}" wx:key="this">
      <block wx:if="{{item.node=='text'}}">{{item.text}}</block>
      <image class="wxEmoji" src="{{item.baseSrc}}{{item.text}}" wx:elif="{{item.node=='element'}}"></image>
    </block>
  </view>
</template>
<template name="wxParseImg">
  <image bindload="wxParseImgLoad" bindtap="wxParseImgTap" class="{{item.classStr}} wxParse-{{item.tag}}" data-from="{{item.from}}" data-idx="{{item.imgIndex}}" data-src="{{item.attr.src}}" mode="widthFix" src="{{item.attr.src}}" style="width:{{item.width}}px;"></image>
</template>
<template name="wxParseVideo">
  <view class="{{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
    <video class="{{item.classStr}} wxParse-{{item.tag}}-video" src="{{item.attr.src}}" poster="{{ item.attr.poster }}" controls></video>
  </view>
</template>