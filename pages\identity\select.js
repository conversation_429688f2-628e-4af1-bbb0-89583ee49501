// pages/identity/select.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    identities: [
      {
        key: 'student',
        name: '学生',
        desc: '查看课程、考情信息',
        icon: '👨‍🎓'
      },
      {
        key: 'teacher',
        name: '授课老师',
        desc: '管理课程、考情、学员',
        icon: '👨‍🏫'
      },
      {
        key: 'admin',
        name: '管理老师',
        desc: '管理课程、学员、授课老师',
        icon: '👨‍💼'
      }
    ],
    selectedIdentity: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否已经选择过身份
    if (app.hasSelectedIdentity()) {
      // 如果已选择身份，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  /**
   * 选择身份
   */
  selectIdentity(e) {
    const identity = e.currentTarget.dataset.identity;
    this.setData({
      selectedIdentity: identity
    });
  },

  /**
   * 确认选择身份
   */
  confirmIdentity() {
    if (!this.data.selectedIdentity) {
      wx.showToast({
        title: '请选择您的身份',
        icon: 'none'
      });
      return;
    }

    // 保存用户身份
    app.setUserIdentity(this.data.selectedIdentity);
    
    wx.showToast({
      title: '身份设置成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          // 跳转到首页
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
