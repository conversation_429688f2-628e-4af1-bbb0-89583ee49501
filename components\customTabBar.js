// 自定义tabBar组件
const app = getApp();

Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#667eea",
    list: []
  },

  attached() {
    this.updateTabBar();
  },

  methods: {
    /**
     * 更新tabBar配置
     */
    updateTabBar() {
      const identity = app.getUserIdentity();
      if (!identity) {
        return;
      }

      const tabBarConfig = app.getTabBarConfig(identity);
      this.setData({
        list: tabBarConfig
      });
    },

    /**
     * tabBar点击事件
     */
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;

      wx.switchTab({
        url: '/' + url
      });

      this.setData({
        selected: data.index
      });
    }
  }
});