# 身份选择和动态tabBar功能测试说明

## 功能概述
实现了用户身份选择功能，支持三种身份：学生、授课老师、管理老师，每种身份有不同的tabBar导航菜单。

## 测试步骤

### 1. 首次启动测试
1. 清除小程序缓存（开发者工具 -> 清缓存 -> 全部清除）
2. 重新编译运行小程序
3. 应该自动跳转到身份选择页面

### 2. 身份选择页面测试
1. 查看页面样式是否美观（微信风格的确认按钮）
2. 点击不同身份卡片，查看选中效果
3. 不选择身份直接点击确认，应该提示"请选择您的身份"
4. 选择身份后点击确认，应该显示"身份设置成功"并跳转

### 3. 学生身份测试
1. 选择"学生"身份
2. 应该跳转到课程页面
3. 底部tabBar应该显示：课程、考情、我的（3个菜单）
4. 点击各个菜单，查看页面切换是否正常
5. 页面顶部应该显示"学生"身份标识

### 4. 授课老师身份测试
1. 在我的页面点击"切换身份"
2. 选择"授课老师"身份
3. 应该跳转到课程页面
4. 底部tabBar应该显示：课程、考情、学员、我的（4个菜单）
5. 点击各个菜单，查看页面切换是否正常
6. 页面顶部应该显示"授课老师"身份标识

### 5. 管理老师身份测试
1. 在我的页面点击"切换身份"
2. 选择"管理老师"身份
3. 应该跳转到课程页面
4. 底部tabBar应该显示：课程、学员、授课老师、我的（4个菜单）
5. 点击各个菜单，查看页面切换是否正常
6. 页面顶部应该显示"管理老师"身份标识

### 6. 身份切换测试
1. 在任意身份下，进入"我的"页面
2. 点击"切换身份"按钮
3. 确认弹窗后应该跳转到身份选择页面
4. 重新选择不同身份，验证菜单是否正确切换

### 7. 数据持久化测试
1. 选择任意身份
2. 关闭小程序（模拟器中按Home键）
3. 重新打开小程序
4. 应该直接进入之前选择的身份对应的页面，而不是身份选择页面

## 预期结果

### 学生身份菜单结构
```
课程 | 考情 | 我的
```

### 授课老师身份菜单结构
```
课程 | 考情 | 学员 | 我的
```

### 管理老师身份菜单结构
```
课程 | 学员 | 授课老师 | 我的
```

## 注意事项
1. 目前使用的是临时图标（home.png），建议后续替换为专门的图标
2. 身份选择页面使用了emoji图标作为临时方案
3. 各页面的数据都是模拟数据，实际使用时需要对接真实API
4. tabBar的选中状态会根据当前页面自动更新

## 已知问题
1. 图标资源需要补充（详见README_ICONS.md）
2. 页面数据为模拟数据，需要对接后端API
3. 用户头像使用emoji临时替代

## 下一步优化建议
1. 添加专门的图标资源
2. 对接真实的用户认证和数据API
3. 添加更多的用户信息管理功能
4. 优化页面加载性能
