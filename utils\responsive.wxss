/* 响应式设计通用样式 */

/* 容器最大宽度限制 */
.responsive-container {
  max-width: 750rpx;
  margin: 0 auto;
  width: 100%;
}

/* 内容区域通用样式 */
.content-wrapper {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.identity-tag {
  background: #667eea;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 列表样式 */
.list-container {
  margin-bottom: 20rpx;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
}

.empty-text {
  font-size: 28rpx;
}

/* 按钮样式 */
.btn-primary {
  width: 100%;
  height: 80rpx;
  background: #07c160;
  color: #fff;
  border-radius: 8rpx;
  font-size: 32rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-primary:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.btn-primary:disabled {
  background: #f7f7f7;
  color: #999;
}

/* 小屏幕适配 */
@media screen and (max-width: 375px) {
  .content-wrapper {
    padding: 15rpx;
  }
  
  .card {
    padding: 24rpx 20rpx;
    margin-bottom: 15rpx;
  }
  
  .page-title {
    font-size: 32rpx;
  }
  
  .identity-tag {
    font-size: 22rpx;
    padding: 8rpx 16rpx;
  }
}

/* 大屏幕适配 */
@media screen and (min-width: 768px) {
  .responsive-container {
    max-width: 600rpx;
  }
  
  .content-wrapper {
    max-width: 600rpx;
    padding: 30rpx;
  }
  
  .card {
    padding: 40rpx 35rpx;
  }
}
