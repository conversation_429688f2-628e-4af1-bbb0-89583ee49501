// pages/exam/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    identity: '',
    exams: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkIdentity();
    this.loadExams();
  },

  /**
   * 检查用户身份
   */
  checkIdentity() {
    const identity = app.getUserIdentity();
    if (!identity) {
      app.navigateToIdentitySelect();
      return;
    }
    this.setData({
      identity: identity
    });
  },

  /**
   * 加载考试数据
   */
  loadExams() {
    const mockExams = [
      {
        id: 1,
        name: '期中数学测试',
        date: '2024-01-15',
        time: '09:00-11:00',
        status: '已完成',
        score: 85
      },
      {
        id: 2,
        name: '英语口语考试',
        date: '2024-01-20',
        time: '14:00-16:00', 
        status: '进行中',
        score: null
      }
    ];
    
    this.setData({
      exams: mockExams
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadExams();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
