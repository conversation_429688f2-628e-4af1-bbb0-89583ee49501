<!--pages/exam/index.wxml-->
<view class="container">
  <view class="header">
    <view class="title">考情管理</view>
    <view class="identity-tag">{{identity === 'student' ? '学生' : identity === 'teacher' ? '授课老师' : '管理老师'}}</view>
  </view>

  <view class="exam-list">
    <view class="exam-item" wx:for="{{exams}}" wx:key="id">
      <view class="exam-info">
        <view class="exam-name">{{item.name}}</view>
        <view class="exam-details">
          <text class="exam-date">考试时间：{{item.date}} {{item.time}}</text>
          <text class="exam-score" wx:if="{{item.score !== null}}">考试成绩：{{item.score}}分</text>
        </view>
      </view>
      <view class="exam-status {{item.status === '进行中' ? 'active' : item.status === '已完成' ? 'finished' : 'pending'}}">
        {{item.status}}
      </view>
    </view>
  </view>

  <view wx:if="{{exams.length === 0}}" class="empty">
    <image src="/images/empty.png" mode="aspectFit"></image>
    <text>暂无考试数据</text>
  </view>
</view>
