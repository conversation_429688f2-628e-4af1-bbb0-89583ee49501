<!--pages/identity/select.wxml-->
<view class="container">
  <view class="header">
    <view class="title">请选择您的身份</view>
  </view>

  <view class="identity-list">
    <view 
      class="identity-item {{selectedIdentity === item.key ? 'selected' : ''}}"
      wx:for="{{identities}}"
      wx:key="key"
      data-identity="{{item.key}}"
      bindtap="selectIdentity"
    >
      <view class="identity-icon">
        <text class="emoji-icon">{{item.icon}}</text>
      </view>
      <view class="identity-info">
        <view class="identity-name">{{item.name}}</view>
        <view class="identity-desc">{{item.desc}}</view>
      </view>
      <view class="identity-check">
        <view class="check-icon {{selectedIdentity === item.key ? 'checked' : ''}}"></view>
      </view>
    </view>
  </view>

  <view class="footer">
    <button 
      class="confirm-btn {{selectedIdentity ? 'active' : ''}}" 
      bindtap="confirmIdentity"
      disabled="{{!selectedIdentity}}"
    >
      确认选择
    </button>
  </view>
</view>
