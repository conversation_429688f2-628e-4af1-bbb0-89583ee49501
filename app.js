/**
 * tabBar页面路径列表 (用于链接跳转时判断)
 * tabBarLinks为常量, 无需修改
 */
const tabBarLinks = [
  'pages/index/index',
  'pages/category/index',
  'pages/flow/index',
  'pages/user/index'
];

// 站点配置文件
import siteinfo from './siteinfo.js';

// 工具类
import util from './utils/util.js';

App({

  /**
   * 全局变量
   */
  globalData: {
    user_id: null,
    identity: null, // 用户身份：student, teacher, admin
    hasSelectedIdentity: false // 是否已选择身份
  },

  // api地址
  api_root: siteinfo.siteroot + 'index.php?s=/api/',

  /**
   * 生命周期函数--监听小程序初始化
   */
  onLaunch(e) {
    let _this = this;
    // 小程序主动更新
    _this.updateManager();
  },

  /**
   * 当小程序启动，或从后台进入前台显示，会触发 onShow
   */
  onShow(options) {
    // 获取小程序基础信息
  },

  /**
   * 执行用户登录
   */
  doLogin() {
    // 保存当前页面
    let pages = getCurrentPages();
    if (pages.length) {
      let currentPage = pages[pages.length - 1];
      "pages/login/login" != currentPage.route &&
        wx.setStorageSync("currentPage", currentPage);
    }
    // 跳转授权页面
    wx.navigateTo({
      url: "/pages/login/login"
    });
  },

  /**
   * 当前用户id
   */
  getUserId() {
    return wx.getStorageSync('user_id');
  },

  /**
   * 获取用户身份
   */
  getUserIdentity() {
    return wx.getStorageSync('user_identity') || null;
  },

  /**
   * 设置用户身份
   */
  setUserIdentity(identity) {
    this.globalData.identity = identity;
    this.globalData.hasSelectedIdentity = true;
    wx.setStorageSync('user_identity', identity);
    wx.setStorageSync('has_selected_identity', true);
  },

  /**
   * 检查是否已选择身份
   */
  hasSelectedIdentity() {
    return wx.getStorageSync('has_selected_identity') || false;
  },

  /**
   * 跳转到身份选择页面
   */
  navigateToIdentitySelect() {
    wx.navigateTo({
      url: '/pages/identity/select'
    });
  },

  /**
   * 根据身份获取tabBar配置
   */
  getTabBarConfig(identity) {
    const configs = {
      student: [
        {
          text: "课程",
          pagePath: "pages/course/index",
          iconPath: "/images/home.png",
          selectedIconPath: "/images/home-active.png"
        },
        {
          text: "考情",
          pagePath: "pages/exam/index",
          iconPath: "/images/home.png",
          selectedIconPath: "/images/home-active.png"
        },
        {
          text: "我的",
          pagePath: "pages/user/center",
          iconPath: "/images/user.png",
          selectedIconPath: "/images/user-active.png"
        }
      ],
      teacher: [
        {
          text: "课程",
          pagePath: "pages/course/index",
          iconPath: "/images/home.png",
          selectedIconPath: "/images/home-active.png"
        },
        {
          text: "考情",
          pagePath: "pages/exam/index",
          iconPath: "/images/home.png",
          selectedIconPath: "/images/home-active.png"
        },
        {
          text: "学员",
          pagePath: "pages/student/index",
          iconPath: "/images/home.png",
          selectedIconPath: "/images/home-active.png"
        },
        {
          text: "我的",
          pagePath: "pages/user/center",
          iconPath: "/images/user.png",
          selectedIconPath: "/images/user-active.png"
        }
      ],
      admin: [
        {
          text: "课程",
          pagePath: "pages/course/index",
          iconPath: "/images/home.png",
          selectedIconPath: "/images/home-active.png"
        },
        {
          text: "学员",
          pagePath: "pages/student/index",
          iconPath: "/images/home.png",
          selectedIconPath: "/images/home-active.png"
        },
        {
          text: "授课老师",
          pagePath: "pages/teacher/index",
          iconPath: "/images/home.png",
          selectedIconPath: "/images/home-active.png"
        },
        {
          text: "我的",
          pagePath: "pages/user/center",
          iconPath: "/images/user.png",
          selectedIconPath: "/images/user-active.png"
        }
      ]
    };
    return configs[identity] || configs.student;
  },

  /**
   * 显示成功提示框
   */
  showSuccess(msg, callback) {
    wx.showToast({
      title: msg,
      icon: 'success',
      mask: true,
      duration: 1500,
      success() {
        callback && (setTimeout(function() {
          callback();
        }, 1500));
      }
    });
  },

  /**
   * 显示失败提示框
   */
  showError(msg, callback) {
    wx.showModal({
      title: '友情提示',
      content: msg,
      showCancel: false,
      success(res) {
        callback && callback();
      }
    });
  },

  /**
   * get请求
   */
  _get(url, data, success, fail, complete, check_login) {
    wx.showNavigationBarLoading();
    let _this = this;
    // 构造请求参数
    data = data || {};

    // 构造get请求
    let request = function() {
      data.token = wx.getStorageSync('token');
      wx.request({
        url: _this.api_root + url,
        header: {
          'content-type': 'application/json'
        },
        data: data,
        success(res) {
          if (res.statusCode !== 200 || typeof res.data !== 'object') {
            _this.showError('网络请求出错');
            return false;
          }
          if (res.data.code === -1) {
            // 登录态失效, 重新登录
            wx.hideNavigationBarLoading();
            _this.doLogin();
          } else if (res.data.code === 0) {
            _this.showError(res.data.msg, function() {
              fail && fail(res);
            });
            return false;
          } else {
            success && success(res.data);
          }
        },
        fail(res) {
          _this.showError(res.errMsg, function() {
            fail && fail(res);
          });
        },
        complete(res) {
          wx.hideNavigationBarLoading();
          complete && complete(res);
        },
      });
    };
    // 判断是否需要验证登录
    check_login ? _this.doLogin(request) : request();
  },

  /**
   * post提交
   */
  _post_form(url, data, success, fail, complete, isShowNavBarLoading) {
    let _this = this;

    isShowNavBarLoading || true;
    data.wxapp_id = _this.getWxappId();
    data.token = wx.getStorageSync('token');

    // 在当前页面显示导航条加载动画
    if (isShowNavBarLoading == true) {
      wx.showNavigationBarLoading();
    }
    wx.request({
      url: _this.api_root + url,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      method: 'POST',
      data: data,
      success(res) {
        if (res.statusCode !== 200 || typeof res.data !== 'object') {
          _this.showError('网络请求出错');
          return false;
        }
        if (res.data.code === -1) {
          // 登录态失效, 重新登录
          wx.hideNavigationBarLoading();
          _this.doLogin();
          return false;
        } else if (res.data.code === 0) {
          _this.showError(res.data.msg, function() {
            fail && fail(res);
          });
          return false;
        }
        success && success(res.data);
      },
      fail(res) {
        // console.log(res);
        _this.showError(res.errMsg, function() {
          fail && fail(res);
        });
      },
      complete(res) {
        wx.hideNavigationBarLoading();
        // wx.hideLoading();
        complete && complete(res);
      }
    });
  },

  /**
   * 验证是否存在user_info
   */
  validateUserInfo() {
    let user_info = wx.getStorageSync('user_info');
    return !!wx.getStorageSync('user_info');
  },

  /**
   * 小程序主动更新
   */
  updateManager() {
    if (!wx.canIUse('getUpdateManager')) {
      return false;
    }
    const updateManager = wx.getUpdateManager();
    updateManager.onCheckForUpdate(function(res) {
      // 请求完新版本信息的回调
      // console.log(res.hasUpdate)
    });
    updateManager.onUpdateReady(function() {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，即将重启应用',
        showCancel: false,
        success(res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate()
          }
        }
      });
    });
    updateManager.onUpdateFailed(function() {
      // 新的版本下载失败
      wx.showModal({
        title: '更新提示',
        content: '新版本下载失败',
        showCancel: false
      })
    });
  },

  /**
   * 获取tabBar页面路径列表
   */
  getTabBarLinks() {
    return tabBarLinks;
  },

  /**
   * 跳转到指定页面
   * 支持tabBar页面
   */
  navigationTo(url) {
    if (!url || url.length == 0) {
      return false;
    }
    let tabBarLinks = this.getTabBarLinks();
    // tabBar页面
    if (tabBarLinks.indexOf(url) > -1) {
      wx.switchTab({
        url: '/' + url
      });
    } else {
      // 普通页面
      wx.navigateTo({
        url: '/' + url
      });
    }
  },

  /**
   * 生成转发的url参数
   */
  getShareUrlParams(params) {
    let _this = this;
    return util.urlEncode(Object.assign({
      referee_id: _this.getUserId()
    }, params));
  },

  /**
   * 发起微信支付
   */
  wxPayment(option) {
    let options = Object.assign({
      payment: {},
      success: () => {},
      fail: () => {},
      complete: () => {},
    }, option);
    wx.requestPayment({
      timeStamp: options.payment.timeStamp,
      nonceStr: options.payment.nonceStr,
      package: 'prepay_id=' + options.payment.prepay_id,
      signType: 'MD5',
      paySign: options.payment.paySign,
      success(res) {
        options.success(res);
      },
      fail(res) {
        options.fail(res);
      },
      complete(res) {
        options.complete(res);
      }
    });
  },
});