// pages/student/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    identity: '',
    students: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkIdentity();
    this.loadStudents();
  },

  /**
   * 检查用户身份
   */
  checkIdentity() {
    const identity = app.getUserIdentity();
    if (!identity) {
      app.navigateToIdentitySelect();
      return;
    }
    this.setData({
      identity: identity
    });
  },

  /**
   * 加载学员数据
   */
  loadStudents() {
    const mockStudents = [
      {
        id: 1,
        name: '张小明',
        avatar: '/images/avatar1.png',
        course: '数学基础课程',
        progress: 75,
        lastLogin: '2024-01-15'
      },
      {
        id: 2,
        name: '李小红',
        avatar: '/images/avatar2.png', 
        course: '英语口语训练',
        progress: 60,
        lastLogin: '2024-01-14'
      },
      {
        id: 3,
        name: '王小华',
        avatar: '/images/avatar3.png',
        course: '数学基础课程', 
        progress: 90,
        lastLogin: '2024-01-16'
      }
    ];
    
    this.setData({
      students: mockStudents
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkIdentity();
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateTabBar();
      const selectedIndex = this.data.identity === 'teacher' ? 2 : 1;
      this.getTabBar().setData({
        selected: selectedIndex
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadStudents();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
