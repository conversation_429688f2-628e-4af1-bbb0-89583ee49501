# 图标文件说明

为了完整实现身份选择和动态tabBar功能，您需要在 `images/` 目录下添加以下图标文件：

## TabBar 图标 (48x48px 推荐)
- `course.png` - 课程图标（未选中状态）
- `course-active.png` - 课程图标（选中状态）
- `exam.png` - 考情图标（未选中状态）
- `exam-active.png` - 考情图标（选中状态）
- `student.png` - 学员图标（未选中状态）
- `student-active.png` - 学员图标（选中状态）
- `teacher.png` - 授课老师图标（未选中状态）
- `teacher-active.png` - 授课老师图标（选中状态）
- `user.png` - 我的图标（未选中状态）
- `user-active.png` - 我的图标（选中状态）

## 身份选择页面图标 (80x80px 推荐)
- `student-role.png` - 学生身份图标
- `teacher-role.png` - 授课老师身份图标
- `admin-role.png` - 管理老师身份图标

## 其他图标
- `empty.png` - 空状态图标
- `default-avatar.png` - 默认头像
- `switch-icon.png` - 切换身份图标
- `setting-icon.png` - 设置图标
- `help-icon.png` - 帮助图标

## 图标设计建议
1. 使用统一的设计风格
2. 选中状态的图标可以使用主题色 #667eea
3. 未选中状态使用灰色 #999999
4. 图标应该简洁明了，易于识别
5. 建议使用 PNG 格式，支持透明背景

## 临时解决方案
如果暂时没有图标，可以：
1. 使用现有的 `home.png` 和 `home-active.png` 作为临时图标
2. 或者从图标库（如 iconfont）下载相应图标
3. 使用在线图标生成工具创建简单图标

## 图标库推荐
- Iconfont (阿里巴巴矢量图标库): https://www.iconfont.cn/
- Feather Icons: https://feathericons.com/
- Material Icons: https://material.io/icons/
