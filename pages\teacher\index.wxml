<!--pages/teacher/index.wxml-->
<view class="container">
  <view class="header">
    <view class="title">授课老师</view>
    <view class="identity-tag">管理老师</view>
  </view>

  <view class="teacher-list">
    <view class="teacher-item" wx:for="{{teachers}}" wx:key="id">
      <view class="teacher-avatar">
        <text class="avatar-emoji">{{item.avatar}}</text>
      </view>
      <view class="teacher-info">
        <view class="teacher-name">{{item.name}}</view>
        <view class="teacher-subject">学科：{{item.subject}}</view>
        <view class="teacher-experience">{{item.experience}}</view>
      </view>
      <view class="teacher-stats">
        <view class="stat-item">
          <view class="stat-number">{{item.courses}}</view>
          <view class="stat-label">课程</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{item.students}}</view>
          <view class="stat-label">学员</view>
        </view>
      </view>
    </view>
  </view>

  <view wx:if="{{teachers.length === 0}}" class="empty">
    <text class="empty-icon">👨‍🏫</text>
    <text>暂无授课老师数据</text>
  </view>
</view>
